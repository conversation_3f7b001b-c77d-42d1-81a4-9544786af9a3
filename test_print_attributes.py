"""
Test script for the E3 Device and Component Attributes Printer

This script tests the print_device_component_attributes.py script
to ensure it works correctly with an E3 project.
"""

import sys
import os

# Add the apps directory to the path so we can import the script
sys.path.append(os.path.join(os.path.dirname(__file__), 'apps'))

try:
    from print_device_component_attributes import E3AttributePrinter
    
    def test_attribute_printer():
        """Test the attribute printer"""
        print("Testing E3 Device and Component Attributes Printer...")
        print("=" * 60)
        
        printer = E3AttributePrinter()
        
        # Test connection
        print("1. Testing E3 connection...")
        if printer.connect_to_e3():
            print("   ✓ Successfully connected to E3")
        else:
            print("   ✗ Failed to connect to E3")
            print("   Make sure E3 is running with a project open")
            return False
        
        # Test attribute printing
        print("\n2. Testing attribute printing...")
        try:
            printer.print_first_ten_devices_attributes()
            print("   ✓ Attribute printing completed")
            return True
        except Exception as e:
            print(f"   ✗ Error during attribute printing: {e}")
            return False
        finally:
            printer.cleanup()
    
    if __name__ == "__main__":
        success = test_attribute_printer()
        if success:
            print("\n" + "=" * 60)
            print("All tests passed! The script is ready to use.")
        else:
            print("\n" + "=" * 60)
            print("Tests failed. Please check the error messages above.")
            sys.exit(1)

except ImportError as e:
    print(f"Error importing the attribute printer script: {e}")
    print("Make sure the print_device_component_attributes.py file exists in the apps directory")
    sys.exit(1)
