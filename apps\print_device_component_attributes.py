"""
E3 Device and Component Attributes Printer

This script prints out all device attributes and component attributes 
for the first ten components in an E3 project.

Author: Generated for E3 automation
"""

import win32com.client
import sys


class E3AttributePrinter:
    def __init__(self):
        self.app = None
        self.job = None
        self.device = None
        self.component = None
        self.attribute = None
        
    def connect_to_e3(self):
        """Connect to E3 application"""
        try:
            self.app = win32com.client.GetActiveObject("CT.Application")
            self.job = self.app.CreateJobObject()
            self.device = self.job.CreateDeviceObject()
            self.component = self.job.CreateComponentObject()
            self.attribute = self.job.CreateAttributeObject()
            print("Successfully connected to E3 application")
            return True
        except Exception as e:
            print(f"Error connecting to E3: {e}")
            return False
    
    def print_device_attributes(self, device_id, device_name, device_assignment, device_location):
        """Print all attributes for a device"""
        try:
            # Set the device ID
            self.device.SetId(device_id)
            
            # Get all attribute IDs for this device
            attribute_ids = []
            attribute_count = self.device.GetAttributeIds(attribute_ids)
            
            print(f"\n  Device Attributes ({attribute_count} found):")
            
            if attribute_count > 0:
                for i in range(1, attribute_count + 1):  # 1-based array
                    try:
                        # Set the attribute ID and get name and value
                        self.attribute.SetId(attribute_ids[i-1])
                        attr_name = self.attribute.GetName()
                        attr_value = self.attribute.GetValue()
                        
                        # Handle empty values
                        if not attr_value or attr_value == "<Empty>":
                            attr_value = "<No Value>"
                        
                        print(f"    {attr_name}: {attr_value}")
                    except Exception as e:
                        print(f"    Error reading attribute {i}: {e}")
            else:
                print("    No device attributes found")
                
        except Exception as e:
            print(f"  Error getting device attributes: {e}")
    
    def print_component_attributes(self, device_id):
        """Print all attributes for the component associated with a device"""
        try:
            # Set the component ID using the device ID
            component_id = self.component.SetId(device_id)
            
            if component_id > 0:
                component_name = self.component.GetName()
                
                # Get all attribute IDs for this component
                attribute_ids = []
                attribute_count = self.component.GetAttributeIds(attribute_ids)
                
                print(f"\n  Component '{component_name}' Attributes ({attribute_count} found):")
                
                if attribute_count > 0:
                    for i in range(1, attribute_count + 1):  # 1-based array
                        try:
                            # Set the attribute ID and get name and value
                            self.attribute.SetId(attribute_ids[i-1])
                            attr_name = self.attribute.GetName()
                            attr_value = self.attribute.GetValue()
                            
                            # Handle empty values
                            if not attr_value or attr_value == "<Empty>":
                                attr_value = "<No Value>"
                            
                            print(f"    {attr_name}: {attr_value}")
                        except Exception as e:
                            print(f"    Error reading component attribute {i}: {e}")
                else:
                    print("    No component attributes found")
            else:
                print("\n  No component found for this device")
                
        except Exception as e:
            print(f"  Error getting component attributes: {e}")
    
    def print_first_ten_devices_attributes(self):
        """Print attributes for the first ten devices in the project"""
        try:
            # Get all device IDs
            device_ids = []
            device_count = self.job.GetAllDeviceIds(device_ids)
            
            if device_count == 0:
                print("No devices found in the project")
                return
            
            # Limit to first 10 devices
            devices_to_process = min(10, device_count)
            print(f"Found {device_count} devices in project. Processing first {devices_to_process}:")
            print("=" * 80)
            
            for i in range(devices_to_process):
                try:
                    device_id = device_ids[i]
                    
                    # Set device and get basic info
                    self.device.SetId(device_id)
                    device_name = self.device.GetName()
                    device_assignment = self.device.GetAssignment()
                    device_location = self.device.GetLocation()
                    
                    print(f"\nDevice {i+1}: {device_name}")
                    print(f"  Assignment: {device_assignment}")
                    print(f"  Location: {device_location}")
                    print(f"  ID: {device_id}")
                    
                    # Print device attributes
                    self.print_device_attributes(device_id, device_name, device_assignment, device_location)
                    
                    # Print component attributes
                    self.print_component_attributes(device_id)
                    
                    print("-" * 80)
                    
                except Exception as e:
                    print(f"Error processing device {i+1}: {e}")
                    continue
                    
        except Exception as e:
            print(f"Error getting device list: {e}")
    
    def cleanup(self):
        """Clean up COM objects"""
        try:
            if self.attribute:
                self.attribute = None
            if self.component:
                self.component = None
            if self.device:
                self.device = None
            if self.job:
                self.job = None
            if self.app:
                self.app = None
        except:
            pass
    
    def run(self):
        """Main execution method"""
        print("E3 Device and Component Attributes Printer")
        print("=" * 50)
        
        if not self.connect_to_e3():
            return False
        
        try:
            self.print_first_ten_devices_attributes()
            return True
        except Exception as e:
            print(f"Error during execution: {e}")
            return False
        finally:
            self.cleanup()


def main():
    """Main function"""
    printer = E3AttributePrinter()
    success = printer.run()
    
    if success:
        print("\nScript completed successfully!")
    else:
        print("\nScript completed with errors!")
        sys.exit(1)


if __name__ == "__main__":
    main()
